import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import LandingPage from "./pages/LandingPage";
import Home from "./pages/Home";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import Profile from "./pages/Profile";
import UserProfile from "./pages/UserProfile.tsx";
import MapPage from "./pages/MapPage";
import VerifyAccount from "./pages/VerifyAccount";
import JobsPage from "./pages/JobsPage";
import ShopPage from "./pages/ShopPage";
import PaymentSuccessPage from "./pages/PaymentSuccessPage";
import PaymentCancelPage from "./pages/PaymentCancelPage";
import { useAuthGuard } from "./hooks/useAuthGuard";
import StatesList from "./components/states/StatesList";
import StateDetail from "./components/states/StateDetail";
import MyStateRedirect from "./components/states/MyStateRedirect";
import WarsPage from "./pages/WarsPage";
import WarDetailPage from "./pages/WarDetailPage";
import DeclareWarPage from "./pages/DeclareWarPage";
import WarAnalyticsPage from "./pages/WarAnalyticsPage";
import CreateParty from "./pages/CreateParty";
import PartyDetailPage from "./pages/PartyDetailPage";
import TravelPermissions from "./pages/TravelPermissions";
import RegionDetailPage from "./pages/RegionDetailPage";

// Create an AuthGuard component to protect routes
const AuthGuard = ({ children }) => {
  // Call the hook to perform the authentication check
  useAuthGuard();

  // If the hook doesn't redirect, render the children
  return children;
};

function App() {
  return (
    <Routes>
      {/* Public routes */}
      <Route path="/" element={<LandingPage />} />
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password" element={<ResetPassword />} />
      <Route path="/verify-account" element={<VerifyAccount />} />

      {/* Protected routes */}
      <Route
        path="/profile"
        element={
          <AuthGuard>
            <Profile />
          </AuthGuard>
        }
      />
      <Route
        path="/home"
        element={
          <AuthGuard>
            <Home />
          </AuthGuard>
        }
      />
      <Route
        path="/map"
        element={
          <AuthGuard>
            <MapPage />
          </AuthGuard>
        }
      />
      <Route
        path="/jobs"
        element={
          <AuthGuard>
            <JobsPage />
          </AuthGuard>
        }
      />
      <Route
        path="/party/create"
        element={
          <AuthGuard>
            <CreateParty />
          </AuthGuard>
        }
      />
      <Route
        path="/party/:id"
        element={
          <AuthGuard>
            <PartyDetailPage />
          </AuthGuard>
        }
      />
      <Route
        path="/shop"
        element={
          <AuthGuard>
            <ShopPage />
          </AuthGuard>
        }
      />
      <Route
        path="/payment/success"
        element={
          <AuthGuard>
            <PaymentSuccessPage />
          </AuthGuard>
        }
      />
      <Route
        path="/payment/cancel"
        element={
          <AuthGuard>
            <PaymentCancelPage />
          </AuthGuard>
        }
      />

      {/* State routes - protected */}
      <Route
        path="/states"
        element={
          <AuthGuard>
            <StatesList />
          </AuthGuard>
        }
      />
      <Route
        path="/states/my-state"
        element={
          <AuthGuard>
            <MyStateRedirect />
          </AuthGuard>
        }
      />
      <Route
        path="/states/:id"
        element={
          <AuthGuard>
            <StateDetail />
          </AuthGuard>
        }
      />

      {/* War routes - protected */}
      <Route
        path="/wars"
        element={
          <AuthGuard>
            <WarsPage />
          </AuthGuard>
        }
      />
      <Route
        path="/wars/new"
        element={
          <AuthGuard>
            <DeclareWarPage />
          </AuthGuard>
        }
      />
      <Route
        path="/wars/:id"
        element={
          <AuthGuard>
            <WarDetailPage />
          </AuthGuard>
        }
      />
      <Route
        path="/wars/analytics"
        element={
          <AuthGuard>
            <WarAnalyticsPage />
          </AuthGuard>
        }
      />

      {/* User routes - protected */}
      <Route
        path="/users/:id"
        element={
          <AuthGuard>
            <UserProfile />
          </AuthGuard>
        }
      />

      {/* Region routes - protected */}
      {/* <Route path="/regions" element={<AuthGuard><RegionsList /></AuthGuard>} /> */}
      <Route
        path="/regions/:id"
        element={
          <AuthGuard>
            <RegionDetailPage />
          </AuthGuard>
        }
      />

      {/* Travel routes - protected */}
      <Route
        path="/travel/permissions"
        element={
          <AuthGuard>
            <TravelPermissions />
          </AuthGuard>
        }
      />

      {/* Redirect to home if logged in, landing if not */}
      <Route path="*" element={<Navigate to="/home" replace />} />
    </Routes>
  );
}

export default App;
