import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useAuthStore from "../store/useAuthStore";
import { isTokenValid } from "../utils/isTokenValid";

/**
 * Hook for public pages (login, register, etc.) to redirect authenticated users
 * to the home page or a specified redirect path
 */
export function useRedirectIfAuthenticated(redirectTo = "/home") {
  const navigate = useNavigate();
  const { user, access_token } = useAuthStore();

  useEffect(() => {
    const isValid = isTokenValid(access_token);
    if (user && isValid) {
      navigate(redirectTo);
    }
  }, [user, access_token, navigate, redirectTo]);
}
