import { useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../services/api/api";
import useAuthStore from "../store/useAuthStore";
import useUserDataStore from "../store/useUserDataStore";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import useChatStore from '../store/useChatStore.js';
import { chatService } from '../services/api/chat.service.js';
import { useRedirectIfAuthenticated } from '../hooks/useRedirectIfAuthenticated';
import { debugAuthIssue, testSpecialCharacters } from '../utils/debugAuth.js';

export default function Login() {
  // Redirect to home if user is already authenticated
  useRedirectIfAuthenticated();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const { fetchUserData } = useUserDataStore();
  const [showPassword, setShowPassword] = useState(false);
  const { setTotalUnreadCount  } = useChatStore();

  const fetchUnreadCounts = async () => {
  try {
    const  data = await chatService.fetchUnreadCounts();

    setTotalUnreadCount(data.unreadCount);
  } catch (error) {
    console.error('Failed to fetch unread counts', error);
  }
};

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data, status } = await api.post("/auth/login", { email, password });
      if (status === 201) {
        // Run comprehensive debug analysis
        debugAuthIssue(data.user?.username, data.access_token);

        login(data.user, data.access_token);

        await fetchUnreadCounts();
        navigate("/home");
      }
    } catch (error) {
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0F0F10] via-[#1C1C1E] to-[#1E2A4A] flex items-center justify-center px-4">
      <div className="w-full max-w-md bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 shadow-xl font-military">
        <div className="text-center mb-6">
          <img src="/wn-logo.png" alt="WarFront Nations" className="h-14 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white">LOGIN</h2>
          <p className="mt-1 text-sm text-[#A1A1A1]">Some nations rise, others fall... but you lead them all</p>
        </div>

        <form onSubmit={handleLogin}>
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="w-full mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
          />

          <div className="relative mb-4">
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-4 py-3 pr-12 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
            />
            <span
              className="absolute right-4 top-3.5 text-[#888] cursor-pointer"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </span>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-[#1C1C1E] border border-white/20 text-white font-bold py-3 rounded-xl transition-all hover:bg-[#2D2D2E]"
          >
            {loading ? "Loading..." : "LOGIN"}
          </button>
        </form>

        <div className="mt-6 text-sm text-center text-[#A1A1A1] space-y-1">
          <p>
            <a href="/forgot-password" className="hover:text-white">Forgot Password?</a>
          </p>
          <p>
            <a href="/register" className="hover:text-white">Create New Account</a>
          </p>
        </div>
      </div>
    </div>
  );
}
