import React, { useState, useEffect } from "react";
import { travelService } from "../../services/api/travel.service";
import { showErrorToast } from "../../utils/showErrorToast";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { FaCheck, FaTimes, FaHourglass } from "react-icons/fa";
import { Link } from "react-router-dom";

const TravelPermissionList = ({ isStateLeader = false, ledState = null }) => {
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [respondingTo, setRespondingTo] = useState(null);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      let permissionData;

      if (isStateLeader) {
        // Get permission requests for the state led by the user
        permissionData = await travelService.getStatePermissionRequests();

        // If we have a state object, we can filter by state ID if needed
        // But since a user can only lead one state, this is likely unnecessary
      } else {
        permissionData = await travelService.getUserPermissionRequests();
      }
      console.log(permissionData, "permissionData");
      setPermissions(permissionData);
    } catch (err) {
      console.error("Error fetching permission requests:", err);
      showErrorToast("Failed to load permission requests");
    } finally {
      setLoading(false);
    }
  };

  const handleRespond = async (requestId, approve, message = "") => {
    try {
      setRespondingTo(requestId);
      await travelService.respondToPermissionRequest(requestId, {
        approve,
        message: message || undefined,
      });

      showSuccessToast(
        `Request ${approve ? "approved" : "rejected"} successfully`
      );
      fetchPermissions();
    } catch (err) {
      console.error("Error responding to permission request:", err);
      showErrorToast("Failed to respond to request");
    } finally {
      setRespondingTo(null);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [isStateLeader]);

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-4 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-1/2 mb-4"></div>
        <div className="space-y-3">
          <div className="h-20 bg-gray-700 rounded"></div>
          <div className="h-20 bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (permissions.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-3">
          {isStateLeader
            ? "Travel Permission Requests"
            : "Your Permission Requests"}
        </h3>
        <p className="text-gray-400">No permission requests found.</p>
      </div>
    );
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return (
          <span className="bg-yellow-900/50 text-yellow-400 px-2 py-1 rounded text-xs flex items-center">
            <FaHourglass className="mr-1" /> Pending
          </span>
        );
      case "approved":
        return (
          <span className="bg-green-900/50 text-green-400 px-2 py-1 rounded text-xs flex items-center">
            <FaCheck className="mr-1" /> Approved
          </span>
        );
      case "rejected":
        return (
          <span className="bg-red-900/50 text-red-400 px-2 py-1 rounded text-xs flex items-center">
            <FaTimes className="mr-1" /> Rejected
          </span>
        );
      default:
        return (
          <span className="bg-gray-700 text-gray-400 px-2 py-1 rounded text-xs">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-white mb-4">
        {isStateLeader
          ? "Travel Permission Requests"
          : "Your Permission Requests"}
      </h3>

      <div className="space-y-4">
        {permissions.map((permission) => (
          <div key={permission.id} className="bg-gray-700/50 rounded-lg p-3">
            <div className="flex flex-col sm:flex-row justify-between gap-3">
              {/* Left content */}
              <div className="flex-1">
                {isStateLeader ? (
                  <Link
                    to={`/users/${permission.user?.id}`}
                    className="text-white font-medium"
                  >
                    {permission.user?.username || "Unknown User"}
                  </Link>
                ) : (
                  <p className="text-white font-medium">
                    {permission.destinationRegion?.name || "Unknown Region"}
                  </p>
                )}

                <p className="text-gray-400 text-sm">
                  Destination: {permission.destinationRegion?.name || "Unknown"}
                  {permission.destinationRegion?.state && (
                    <span> ({permission.destinationRegion.state.name})</span>
                  )}
                </p>

                {permission.reason && (
                  <p className="text-gray-300 text-sm mt-2 bg-gray-800/50 p-2 rounded">
                    <span className="text-gray-400">Reason:</span>{" "}
                    {permission.reason}
                  </p>
                )}

                {permission.responseMessage && (
                  <p className="text-gray-300 text-sm mt-2 bg-gray-800/50 p-2 rounded">
                    <span className="text-gray-400">Response:</span>{" "}
                    {permission.responseMessage}
                  </p>
                )}

                <p className="text-gray-400 text-xs mt-2">
                  Requested: {new Date(permission.createdAt).toLocaleString()}
                </p>
              </div>

              {/* Right content - status and buttons */}
              <div className="flex flex-col items-end sm:items-start gap-2">
                {getStatusBadge(permission.status)}

                {isStateLeader && permission.status === "pending" && (
                  <div className="flex flex-row gap-2 w-full sm:w-auto justify-end sm:justify-start">
                    <button
                      onClick={() => handleRespond(permission.id, true)}
                      disabled={respondingTo === permission.id}
                      className="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded text-sm disabled:bg-gray-600 flex-1 sm:flex-none min-w-[100px]"
                    >
                      {respondingTo === permission.id
                        ? "Processing..."
                        : "Approve"}
                    </button>
                    <button
                      onClick={() => handleRespond(permission.id, false)}
                      disabled={respondingTo === permission.id}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded text-sm disabled:bg-gray-600 flex-1 sm:flex-none min-w-[100px]"
                    >
                      {respondingTo === permission.id
                        ? "Processing..."
                        : "Reject"}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TravelPermissionList;
