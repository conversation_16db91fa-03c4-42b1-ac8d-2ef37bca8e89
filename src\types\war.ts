import { Region } from './region';
import { State } from './state';
import { User } from './user';

export enum WarType {
  GROUND = 'ground',
  SEA = 'sea',
  AIR = 'air',
  REVOLUTION = 'revolution'
}

export enum WarTarget {
  CONQUEST = 'conquest',
  RESISTANCE = 'resistance',
  RESOURCES = 'resources',
  REVOLUTION = 'revolution'
}

export enum WarStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  ENDED = 'ended'
}

export interface BattleEvent {
  timestamp: Date;
  description: string;
  damage: number;
  side: 'attacker' | 'defender';
  userId: number;
}

export interface WarParticipant {
  userId: number;
  damage: number;
}

export interface War {
  id: string;
  warType: WarType;
  status: WarStatus;
  warTarget: WarTarget;
  declaration?: string;
  attackerState?: State;
  attackerRegion?: Region;
  defenderState?: State;
  defenderRegion?: Region;
  targetRegion?: Region;
  declaredAt: Date;
  startedAt?: Date;
  endedAt?: Date;
  seaPhaseStartedAt?: Date;
  seaPhaseEndedAt?: Date;
  seaPhaseWon: boolean;
  groundPhaseStartedAt?: Date;
  attackerSeaDamage: number;
  defenderSeaDamage: number;
  attackerGroundDamage: number;
  defenderGroundDamage: number;
  revolutionSupportPercentage?: number;
  resourcesTarget?: {
    gold?: number;
    oil?: number;
    ore?: number;
    uranium?: number;
    diamonds?: number;
  };
  damageRequirement?: number;
  attackerBonus: number;
  defenderBonus: number;
  battleEvents: BattleEvent[];
  participants: {
    attackers: WarParticipant[];
    defenders: WarParticipant[];
  };
  cooldownUntil?: Date;
  declaredBy: User;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWarDto {
  warType: WarType;
  warTarget: WarTarget;
  declaration?: string;
  attackerStateId?: string;
  attackerRegionId?: string;
  defenderStateId?: string;
  defenderRegionId: string;
  targetRegionId?: string;
  resourcesTarget?: {
    gold?: number;
    oil?: number;
    ore?: number;
    uranium?: number;
    diamonds?: number;
  };
}

export interface ParticipateInWarDto {
  energyAmount: number;
  autoMode?: boolean;
  autoEnergyPercentage?: number;
  side?: 'attacker' | 'defender'; // Required for revolution wars
}