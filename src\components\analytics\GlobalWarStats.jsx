import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';

const GlobalWarStats = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGlobalStats = async () => {
      try {
        setLoading(true);
        const data = await warService.getGlobalWarStats();
        setStats(data);
      } catch (error) {
        console.error('Failed to fetch global war statistics:', error);
        toast.error('Failed to load war statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchGlobalStats();
  }, []);

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <p className="text-gray-400">No war statistics available</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-white mb-4">Global War Statistics</h2>

      <div className="grid grid-cols-2 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Total Wars</h3>
            <span className="text-neonBlue text-2xl">⚔️</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.totalWars}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Active Wars</h3>
            <span className="text-red-500 text-2xl">🔥</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.totalWars - stats.endedWars}</p>
        </div>

        {/* <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Ended Wars</h3>
            <span className="text-green-500 text-2xl">✓</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.endedWars}</p>
        </div> */}

        {/* <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Avg. Duration</h3>
            <span className="text-yellow-500 text-2xl">⏱️</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.averageDuration.toFixed(1)} days</p>
        </div> */}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">War Types</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Ground Wars:</span>
              <span className="text-white">{stats.groundWars}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Sea Wars:</span>
              <span className="text-white">{stats.seaWars}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Revolution Wars:</span>
              <span className="text-white">{stats.revolutionWars}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-700 p-4 rounded-md">
          <h3 className="text-lg font-medium text-white mb-3">War Targets</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Conquest Wars:</span>
              <span className="text-white">{stats.conquestWars}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Resource Wars:</span>
              <span className="text-white">{stats.resourceWars}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-medium text-white mb-3">Most Active Regions</h3>
        <div className="bg-gray-700 rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-600">
            <thead className="bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Region
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                  War Count
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {stats.mostActiveRegions.map((region) => (
                <tr key={region.regionId}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link to={`/regions/${region.regionId}`} className="text-neonBlue hover:text-blue-400">
                      {region.regionName}
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-white">
                    {region.warCount}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-medium text-white mb-3">Most Active States</h3>
        <div className="bg-gray-700 rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-600">
            <thead className="bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  State
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                  War Count
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {stats.mostActiveStates.map((state) => (
                <tr key={state.stateId}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link to={`/states/${state.stateId}`} className="text-neonBlue hover:text-blue-400">
                      {state.stateName}
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-white">
                    {state.warCount}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mt-6 text-center">
        <Link to="/wars" className="inline-block px-4 py-2 bg-neonBlue rounded-md hover:bg-blue-600 transition-colors">
          View All Wars
        </Link>
      </div>
    </div>
  );
};

export default GlobalWarStats;
