import React from 'react';
import Navbar from '../components/Navbar';
import DeclareWarForm from '../components/wars/DeclareWarForm';
import { useNavigate } from 'react-router-dom';
import { useAuthGuard } from '../hooks/useAuthGuard';

const DeclareWarPage = () => {
  useAuthGuard();
  const navigate = useNavigate();

  const handleSuccess = (newWar) => {
    // If newWar exists and has an id, navigate to the specific war page
    if (newWar && newWar.id) {
      navigate(`/wars/${newWar.id}`);
    } else {
      // Otherwise, navigate to the wars list page
      navigate('/wars');
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <DeclareWarForm onSuccess={handleSuccess} />
      </div>
    </div>
  );
};

export default DeclareWarPage;