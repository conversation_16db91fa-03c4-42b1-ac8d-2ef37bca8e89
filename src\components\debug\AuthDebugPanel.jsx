import { useState } from 'react';
import useAuthStore from '../../store/useAuthStore';
import { debugAuthIssue, testSpecialCharacters } from '../../utils/debugAuth';

const AuthDebugPanel = () => {
  const [testResults, setTestResults] = useState([]);
  const { login, logout, user, access_token } = useAuthStore();

  const testUsers = [
    { username: '高雄富野渡假酒店', email: '<EMAIL>', id: 1 },
    { username: 'normaluser', email: '<EMAIL>', id: 2 },
    { username: 'café_user', email: '<EMAIL>', id: 3 },
    { username: 'Москва', email: '<EMAIL>', id: 4 },
    { username: '🎮gamer🎯', email: '<EMAIL>', id: 5 },
  ];

  const simulateLogin = (testUser) => {
    console.group(`🧪 Testing login for: ${testUser.username}`);
    
    const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Clear any existing auth data
      logout();
      
      // Simulate login
      login(testUser, mockToken);
      
      // Check if token is accessible
      const storedToken = localStorage.getItem('access_token');
      const storedUser = localStorage.getItem('user');
      
      const result = {
        username: testUser.username,
        success: !!storedToken && !!storedUser,
        tokenStored: !!storedToken,
        userStored: !!storedUser,
        tokenMatches: storedToken === mockToken,
        userParseable: false,
        timestamp: new Date().toISOString()
      };
      
      try {
        const parsedUser = JSON.parse(storedUser);
        result.userParseable = true;
        result.usernamePreserved = parsedUser.username === testUser.username;
      } catch (e) {
        result.parseError = e.message;
      }
      
      console.log('Test result:', result);
      setTestResults(prev => [...prev, result]);
      
      // Run detailed debug
      debugAuthIssue(testUser.username, mockToken);
      
    } catch (error) {
      console.error('Login simulation failed:', error);
      setTestResults(prev => [...prev, {
        username: testUser.username,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }]);
    }
    
    console.groupEnd();
  };

  const runAllTests = () => {
    setTestResults([]);
    testUsers.forEach((testUser, index) => {
      setTimeout(() => simulateLogin(testUser), index * 100);
    });
  };

  const clearResults = () => {
    setTestResults([]);
    logout();
  };

  const runSpecialCharTests = () => {
    testSpecialCharacters();
  };

  return (
    <div className="p-6 bg-gray-900 text-white rounded-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">🔍 Auth Debug Panel</h2>
      
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Current Auth State</h3>
        <div className="bg-gray-800 p-3 rounded">
          <p><strong>User:</strong> {user?.username || 'None'}</p>
          <p><strong>Token:</strong> {access_token ? `${access_token.substring(0, 20)}...` : 'None'}</p>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Test Controls</h3>
        <div className="space-x-2">
          <button 
            onClick={runAllTests}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
          >
            Run All Login Tests
          </button>
          <button 
            onClick={runSpecialCharTests}
            className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded"
          >
            Test Special Characters
          </button>
          <button 
            onClick={clearResults}
            className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded"
          >
            Clear Results
          </button>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Individual Tests</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {testUsers.map((testUser, index) => (
            <button
              key={index}
              onClick={() => simulateLogin(testUser)}
              className="bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-left"
            >
              Test: {testUser.username}
            </button>
          ))}
        </div>
      </div>

      {testResults.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Test Results</h3>
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div 
                key={index} 
                className={`p-3 rounded ${result.success ? 'bg-green-800' : 'bg-red-800'}`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <strong>{result.username}</strong>
                    <div className="text-sm mt-1">
                      <span className={result.tokenStored ? 'text-green-300' : 'text-red-300'}>
                        Token: {result.tokenStored ? '✓' : '✗'}
                      </span>
                      {' | '}
                      <span className={result.userStored ? 'text-green-300' : 'text-red-300'}>
                        User: {result.userStored ? '✓' : '✗'}
                      </span>
                      {' | '}
                      <span className={result.userParseable ? 'text-green-300' : 'text-red-300'}>
                        Parseable: {result.userParseable ? '✓' : '✗'}
                      </span>
                      {result.usernamePreserved !== undefined && (
                        <>
                          {' | '}
                          <span className={result.usernamePreserved ? 'text-green-300' : 'text-red-300'}>
                            Username: {result.usernamePreserved ? '✓' : '✗'}
                          </span>
                        </>
                      )}
                    </div>
                    {result.error && (
                      <div className="text-red-300 text-sm mt-1">
                        Error: {result.error}
                      </div>
                    )}
                    {result.parseError && (
                      <div className="text-red-300 text-sm mt-1">
                        Parse Error: {result.parseError}
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-gray-400">
                    {new Date(result.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthDebugPanel;
