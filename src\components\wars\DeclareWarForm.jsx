import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { WarType, WarTarget } from '../../types/war';
import useAuthStore from '../../store/useAuthStore';
import useUserDataStore from '../../store/useUserDataStore';
import { api } from '../../services/api/api';
import { regionService } from '../../services/api/region.service';
import { stateService } from '../../services/api/state.service';
import { warService } from '../../services/api/war.service';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';

const DeclareWarForm = ({ onSuccess }) => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { userData, fetchUserData } = useUserDataStore();
  const [loading, setLoading] = useState(false);
  const [redirecting, setRedirecting] = useState(false);
  const [regions, setRegions] = useState([]);
  const [availableTargets, setAvailableTargets] = useState([]);
  const [loadingTargets, setLoadingTargets] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    warType: 'ground',
    attackerRegionId: '',
    defenderRegionId: '',
    targetRegionName: '',
    declaration: ''
  });
  const [isStateLeader, setIsStateLeader] = useState(false);
  const [stateId, setStateId] = useState(null);

  // Fetch user data
  useEffect(() => {
    if (!user?.id) return;
    fetchUserData();
  }, [user?.id, fetchUserData, userData]);

  // Fetch user's state ID
  useEffect(() => {
    const fetchUserStateId = async () => {
      if (!user?.id) return;

      try {
        const state = await stateService.getStateLedByUser();
        if (state) {
          setStateId(state.id);
          setRegions(state.regions);
          setIsStateLeader(true);
        } else {
          setStateId(null);
        }
      } catch (err) {
        setError('Failed to fetch user data');
        setStateId(null);
      }
    };

    fetchUserStateId();
  }, [user?.id]);

  // Fetch regions data
  useEffect(() => {
    if (!stateId) return;

    const fetchData = async () => {
      try {
        const regionsResponse = await api.get('/regions');
        setRegions(regionsResponse.data);
      } catch (err) {
        setError('Failed to load data');
        showErrorToast('Failed to load regions');
      }
    };
    fetchData();
  }, [stateId]);

  // Fetch available targets
  const fetchAvailableTargets = async (warType, attackerRegionId) => {
    if (!warType || !attackerRegionId) {
      setAvailableTargets([]);
      return;
    }

    setLoadingTargets(true);
    try {
      const targets = await warService.getAvailableTargets(warType, attackerRegionId);
      setAvailableTargets(targets);
    } catch (error) {
      console.error('Error fetching available targets:', error);
      showErrorToast('Failed to fetch available targets');
      setAvailableTargets([]);
    } finally {
      setLoadingTargets(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (name === 'attackerRegionId' || name === 'warType') {
      const newWarType = name === 'warType' ? value : formData.warType;
      const currentAttackerRegionId = name === 'attackerRegionId' ? value : formData.attackerRegionId;

      let newAttackerRegionId = currentAttackerRegionId;
      if (name === 'warType' && value === 'sea' && currentAttackerRegionId) {
        const currentRegion = regions.find(r => r.id === currentAttackerRegionId);
        if (currentRegion && !currentRegion.seaAccess) {
          newAttackerRegionId = '';
        }
      }

      setFormData(prev => ({
        ...prev,
        defenderRegionId: '',
        targetRegionName: '',
        ...(newAttackerRegionId !== currentAttackerRegionId && { attackerRegionId: newAttackerRegionId })
      }));
      setAvailableTargets([]);

      if (newWarType && newAttackerRegionId) {
        fetchAvailableTargets(newWarType, newAttackerRegionId);
      }
    }
  };

  // Search for region by name
  const searchRegion = async () => {
    if (!formData.targetRegionName.trim()) return;

    try {
      const results = await regionService.searchRegionsByName(formData.targetRegionName);
      if (results && results.length > 0) {
        const foundRegion = results.find(r =>
          r.name.toLowerCase() === formData.targetRegionName.toLowerCase()
        ) || results[0];

        const attackerRegion = regions.find(r => r.id === formData.attackerRegionId);
        if (!attackerRegion) {
          showErrorToast('Please select an attacking region first');
          return;
        }

        if (attackerRegion.bordersWithNames) {
          const isValidTarget = attackerRegion.bordersWithNames.some(name =>
            name.toLowerCase() === foundRegion.name.toLowerCase()
          );
          if (!isValidTarget) {
            showErrorToast(`${foundRegion.name} does not border with ${attackerRegion.name}`);
            return;
          }
        }
        else if (!attackerRegion.bordersWith?.includes(foundRegion.countryCode)) {
          showErrorToast(`${foundRegion.name} does not border with ${attackerRegion.name}`);
          return;
        }

        if (attackerRegion.seaAccess && !foundRegion.seaAccess) {
          showErrorToast(`${foundRegion.name} does not have sea access`);
          return;
        }

        if (foundRegion.state?.id === stateId) {
          showErrorToast(`${foundRegion.name} belongs to your state`);
          return;
        }

        setFormData(prev => ({
          ...prev,
          defenderRegionId: foundRegion.id
        }));
        showSuccessToast(`Found region: ${foundRegion.name}`);
      } else {
        showErrorToast(`No region found with name: ${formData.targetRegionName}`);
      }
    } catch (error) {
      console.error('Error searching for region:', error);
      showErrorToast('Failed to search for region');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (!formData.defenderRegionId && formData.targetRegionName) {
        await searchRegion();
        if (!formData.defenderRegionId) {
          throw new Error('Please select a valid target region');
        }
      }

      if (!formData.attackerRegionId) {
        throw new Error('Please select an attacking region');
      }

      if (!formData.defenderRegionId) {
        throw new Error('Please select a valid target region');
      }

      const defenderRegion = availableTargets.find(r => r.id === formData.defenderRegionId);

      const warData = {
        warType: formData.warType === 'ground' ? WarType.GROUND : WarType.SEA,
        warTarget: WarTarget.CONQUEST,
        declaration: formData.declaration,
        attackerRegionId: formData.attackerRegionId,
        defenderRegionId: formData.defenderRegionId,
      };

      try {
        const createdWar = await warService.declareWar(warData);
        showSuccessToast(`War declared successfully against ${defenderRegion?.name || 'target region'}!`);

        if (createdWar && createdWar.id) {
          localStorage.setItem('lastCreatedWarId', createdWar.id);
        }

        if (onSuccess) {
          onSuccess(createdWar);
        } else {
          navigate('/wars');
        }
      } catch (declareError) {
        console.error('Error during war declaration:', declareError);
        throw declareError;
      }
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to declare war');
      showErrorToast(err);
      setRedirecting(false);
    } finally {
      setLoading(false);
    }
  };

  // Get war type display info
  const getWarTypeInfo = (type) => {
    switch (type) {
      case 'ground':
        return {
          name: 'GROUND WAR',
          duration: '24 HOURS',
          color: 'from-green-600 to-green-700',
          badge: 'bg-gradient-to-r from-green-600 to-green-700'
        };
      case 'sea':
        return {
          name: 'SEA WAR',
          duration: '48 HOURS',
          color: 'from-blue-600 to-blue-700',
          badge: 'bg-gradient-to-r from-blue-600 to-blue-700'
        };
      default:
        return {
          name: 'WAR',
          duration: '72 HOURS',
          color: 'from-purple-600 to-purple-700',
          badge: 'bg-gradient-to-r from-purple-600 to-purple-700'
        };
    }
  };

  return (
    <div className="container mx-auto p-4 relative">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-6">
        <div className="text-center md:text-left">
          <h1 className="text-3xl md:text-4xl font-black text-white uppercase tracking-wide bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent">
            WAR DECLARATION CENTER
          </h1>
          <p className="text-gray-400 mt-2 font-medium">
            Initiate military operations against enemy territories
          </p>
        </div>
      </div>

      {!isStateLeader ? (
        <div className="bg-gradient-to-r from-red-900/80 to-red-900/60 border-l-4 border-red-500 text-yellow-100 p-6 rounded-xl mb-8 flex items-center">
          <svg className="w-8 h-8 text-yellow-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <div>
            <p className="font-bold text-lg">COMMAND AUTHORIZATION REQUIRED</p>
            <p>You must be the leader of your state to declare war.</p>
          </div>
        </div>
      ) : (
        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl shadow-xl overflow-hidden">
          <div className="bg-gradient-to-r from-red-700 to-red-800 py-5 px-6">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-white mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <h2 className="text-xl font-bold uppercase tracking-wide">War Declaration Protocol</h2>
            </div>
          </div>

          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* War Type Selection */}
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-700 to-red-800 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold">War Type</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {['ground', 'sea'].map(type => {
                    const typeInfo = getWarTypeInfo(type);
                    const isSelected = formData.warType === type;
                    
                    return (
                      <div 
                        key={type} 
                        className={`relative transition-all transform cursor-pointer rounded-xl overflow-hidden ${
                          isSelected ? 'ring-4 ring-red-500' : 'ring-1 ring-gray-700'
                        }`}
                        onClick={() => setFormData(prev => ({...prev, warType: type}))}
                      >
                        <div className={`bg-gradient-to-br from-gray-800 to-gray-900/80 p-5 h-full`}>
                          <div className="flex justify-between items-start">
                            <h4 className="text-lg font-extrabold text-white uppercase tracking-wide">
                              {typeInfo.name}
                            </h4>
                            <span className={`px-3 py-1 rounded-lg text-xs font-black uppercase ${typeInfo.badge}`}>
                              {type.slice(0, 3).toUpperCase()}
                            </span>
                          </div>
                          <div className="mt-4">
                            <div className="flex items-center text-sm font-bold text-gray-300 mb-2">
                              <svg className="w-4 h-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              Duration: {typeInfo.duration}
                            </div>
                            <p className="text-gray-400 text-sm">
                              {type === 'ground'
                                ? 'Direct territorial assault with infantry and armored divisions'
                                : 'Naval engagement to establish maritime dominance'}
                            </p>
                          </div>
                          <div className="mt-4">
                            <div className="flex items-center text-sm text-gray-400">
                              <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                              <span>
                                {type === 'ground'
                                  ? 'Requires land border access'
                                  : 'Requires sea access'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              
              {/* Region Selection */}
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-700 to-red-800 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold">Region Selection</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Attacking Region */}
                  <div>
                    <div className="flex items-center mb-3">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                      <label className="font-bold text-gray-300">Attacking Region</label>
                    </div>
                    <div className="relative">
                      <select
                        id="attackerRegionId"
                        name="attackerRegionId"
                        value={formData.attackerRegionId}
                        onChange={handleChange}
                        className="w-full bg-gradient-to-b from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 appearance-none bg-[#1C1C1E] text-white"
                        required
                      >
                        <option value="">Select deployment region</option>
                        {regions
                          .filter(region => {
                            if (region.state?.id !== stateId) return false;
                            if (formData.warType === 'sea' && !region.seaAccess) return false;
                            return true;
                          })
                          .map(region => (
                            <option key={region.id} value={region.id}>
                              {region.name} {formData.warType === 'sea' && region.seaAccess ? '(Sea Access)' : ''}
                            </option>
                          ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4">
                        <svg className="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                    {formData.warType === 'sea' && regions.filter(region => region.state?.id === stateId && region.seaAccess).length === 0 && (
                      <div className="mt-3 flex items-center text-sm text-yellow-400">
                        <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <span>Your state has no regions with sea access</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Target Region */}
                  <div>
                    <div className="flex items-center mb-3">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                      <label className="font-bold text-gray-300">Target Region</label>
                    </div>
                    <div className="relative">
                      <select
                        id="defenderRegionId"
                        name="defenderRegionId"
                        value={formData.defenderRegionId}
                        onChange={handleChange}
                        className="w-full bg-gradient-to-b from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 appearance-none bg-[#1C1C1E] text-white"
                        disabled={!formData.attackerRegionId || loadingTargets}
                      >
                        <option value="">
                          {loadingTargets ? 'Loading targets...' : 'Select target region'}
                        </option>
                        {availableTargets.map(region => (
                          <option key={region.id} value={region.id}>
                            {region.name} {region.state?.name ? `(${region.state.name})` : ''}
                          </option>
                        ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4">
                        <svg className="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {error && (
                <div className="bg-gradient-to-r from-red-900/80 to-red-900/60 border-l-4 border-red-500 text-yellow-100 p-4 rounded-xl flex items-center">
                  <svg className="w-6 h-6 text-yellow-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  <div>
                    <p className="font-bold">TACTICAL ERROR</p>
                    <p className="mt-1">{error}</p>
                  </div>
                </div>
              )}
              
              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row justify-between gap-4 pt-4">
                <button
                  type="button"
                  onClick={() => navigate('/wars')}
                  className="px-6 py-3 bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 border-2 border-gray-700 rounded-xl transition-all font-bold flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Cancel Operation
                </button>
                <button
                  type="submit"
                  disabled={loading || redirecting}
                  className={`px-6 py-3 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 rounded-xl font-bold text-white shadow-lg shadow-red-900/30 transition-all flex items-center justify-center ${
                    (loading || redirecting) ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Declaring War...
                    </>
                  ) : redirecting ? (
                    'Redirecting...'
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 14v6m-3-3h6M6 10h2a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2zm10 0h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2v2a2 2 0 002 2zM6 20h2a2 2 0 002-2v-2a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2z" />
                      </svg>
                      Declare War
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Tactical Footer */}
      <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-gray-800 to-black border-t border-red-500/30 py-3 px-6 z-10">
        <div className="max-w-7xl mx-auto flex justify-center items-center">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span className="text-sm">COMMAND STATUS: <span className="text-green-500 font-bold">OPERATIONAL</span></span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeclareWarForm;