/**
 * Debug utility for authentication issues
 * Helps identify problems with token storage and retrieval
 */

export const debugAuthIssue = (username, token) => {
  console.group('🔍 Auth Debug Analysis');
  
  // Basic info
  console.log('Username:', username);
  console.log('Username type:', typeof username);
  console.log('Username length:', username?.length);
  console.log('Token:', token?.substring(0, 20) + '...');
  console.log('Token type:', typeof token);
  console.log('Token length:', token?.length);
  
  // Character analysis
  if (username) {
    console.group('📝 Username Character Analysis');
    console.log('Characters:', username.split(''));
    console.log('Char codes:', username.split('').map(c => c.charCodeAt(0)));
    console.log('UTF-8 bytes:', Array.from(new TextEncoder().encode(username)));
    console.log('Has non-ASCII:', /[^\x00-\x7F]/.test(username));
    console.log('Has CJK characters:', /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff]/.test(username));
    console.groupEnd();
  }
  
  // localStorage tests
  console.group('💾 LocalStorage Tests');
  
  // Test 1: Basic storage
  const testKey = 'auth_debug_test';
  const testData = { username, timestamp: Date.now() };
  
  try {
    localStorage.setItem(testKey, JSON.stringify(testData));
    const retrieved = JSON.parse(localStorage.getItem(testKey));
    console.log('✅ Basic JSON storage/retrieval works');
    console.log('Original:', testData);
    console.log('Retrieved:', retrieved);
    console.log('Match:', JSON.stringify(testData) === JSON.stringify(retrieved));
    localStorage.removeItem(testKey);
  } catch (error) {
    console.error('❌ Basic JSON storage failed:', error);
  }
  
  // Test 2: Token storage
  const tokenTestKey = 'auth_debug_token';
  try {
    localStorage.setItem(tokenTestKey, token);
    const retrievedToken = localStorage.getItem(tokenTestKey);
    console.log('✅ Token storage works');
    console.log('Token match:', token === retrievedToken);
    localStorage.removeItem(tokenTestKey);
  } catch (error) {
    console.error('❌ Token storage failed:', error);
  }
  
  // Test 3: Current localStorage state
  console.log('Current localStorage keys:', Object.keys(localStorage));
  console.log('Current access_token exists:', !!localStorage.getItem('access_token'));
  console.log('Current user exists:', !!localStorage.getItem('user'));
  
  const currentUser = localStorage.getItem('user');
  if (currentUser) {
    try {
      const parsed = JSON.parse(currentUser);
      console.log('Current stored username:', parsed?.username);
    } catch (e) {
      console.error('Current user data corrupted:', e);
    }
  }
  
  console.groupEnd();
  
  // Browser compatibility tests
  console.group('🌐 Browser Compatibility');
  console.log('localStorage supported:', typeof Storage !== 'undefined');
  console.log('JSON supported:', typeof JSON !== 'undefined');
  console.log('TextEncoder supported:', typeof TextEncoder !== 'undefined');
  console.log('User agent:', navigator.userAgent);
  console.groupEnd();
  
  console.groupEnd();
};

export const testSpecialCharacters = () => {
  console.group('🔤 Special Character Tests');
  
  const testCases = [
    '高雄富野渡假酒店', // Chinese characters
    'café', // Accented characters
    'Москва', // Cyrillic
    'العربية', // Arabic
    '🎮🎯', // Emojis
    '<EMAIL>', // Normal ASCII
  ];
  
  testCases.forEach((testString, index) => {
    console.group(`Test ${index + 1}: "${testString}"`);
    
    const testUser = { username: testString, id: index + 1 };
    const testToken = `test_token_${index}_${Date.now()}`;
    
    try {
      // Test storage
      localStorage.setItem('test_user_' + index, JSON.stringify(testUser));
      localStorage.setItem('test_token_' + index, testToken);
      
      // Test retrieval
      const retrievedUser = JSON.parse(localStorage.getItem('test_user_' + index));
      const retrievedToken = localStorage.getItem('test_token_' + index);
      
      console.log('✅ Storage successful');
      console.log('User match:', JSON.stringify(testUser) === JSON.stringify(retrievedUser));
      console.log('Token match:', testToken === retrievedToken);
      console.log('Username preserved:', testUser.username === retrievedUser.username);
      
      // Cleanup
      localStorage.removeItem('test_user_' + index);
      localStorage.removeItem('test_token_' + index);
      
    } catch (error) {
      console.error('❌ Storage failed:', error);
    }
    
    console.groupEnd();
  });
  
  console.groupEnd();
};

// Auto-run tests when this module is imported in development
if (import.meta.env.DEV) {
  console.log('🔧 Auth debug utilities loaded. Use debugAuthIssue(username, token) or testSpecialCharacters() in console.');
}
