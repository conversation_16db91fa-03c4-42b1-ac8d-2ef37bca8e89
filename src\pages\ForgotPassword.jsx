import { useState } from "react";
import api from "../services/api/api";
import { toast } from "react-toastify";
import { useRedirectIfAuthenticated } from "../hooks/useRedirectIfAuthenticated";

export default function ForgotPassword() {
  // Redirect to home if user is already authenticated
  useRedirectIfAuthenticated();
  const [email, setEmail] = useState("");
  const [submitted, setSubmitted] = useState(false);

  const handleForgotPassword = async (e) => {
    e.preventDefault();
    try {
      await api.post("/auth/forgot-password", { email });
      setSubmitted(true);
      toast.success("If an account exists, you will receive an email shortly.");
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0F0F10] via-[#1C1C1E] to-[#1E2A4A] flex items-center justify-center px-4">
      <div className="w-full max-w-md bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 shadow-xl font-military">
        <div className="text-center mb-6">
          <p className="text-sm text-center text-[#A1A1A1] space-y-1"><a href='/login' className="hover:text-white">Back to login page</a></p><br></br>
          <img src="/wn-logo.png" alt="WarFront Nations" className="h-14 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white">Forgot Password</h2>
        </div>
      <form className="bg-darkCard p-6 rounded-lg" onSubmit={handleForgotPassword}>

        {submitted ? (
          <><p className="text-green-400">
                      If an account exists, you will receive an email shortly.
                  </p><p className="mt-2 text-sm text-gray-400">
                          <a href="/login" className="text-neonBlue">
                              Login
                          </a>
                      </p></>
        ) : (
          <>
            <input
              type="email"
              className="w-full mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <button type="submit" className="w-full bg-[#1C1C1E] border border-white/20 text-white font-bold py-3 rounded-xl transition-all hover:bg-[#2D2D2E]">
              Reset Password
            </button>
          </>
        )}
      </form>
      </div>
    </div>
  );
}
