import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { partyService } from '../services/api/party.service';
import useAuthStore from '../store/useAuthStore';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Navbar from '../components/Navbar';

const CreateParty = () => {
  const navigate = useNavigate();
  useAuthGuard();
  const { user } = useAuthStore();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    regionId: user?.region?.id || '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      if (!formData.name.trim()) {
        throw new Error('Party name is required');
      }

      if (!formData.regionId) {
        throw new Error('Region is required');
      }

      await partyService.createParty(formData);
      navigate('/profile');
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to create party');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-neonBlue mb-6">Create a New Party</h2>
          
          {error && (
            <div className="mb-4 p-4 bg-red-900/50 border border-red-500 text-red-200 rounded">
              {error}
            </div>
          )}

          <div className="mb-6 p-4 bg-gray-700 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">Requirements:</h3>
            <ul className="list-disc list-inside text-gray-300 space-y-1">
              <li>Cost: 200 gold</li>
              <li>You must be in a region</li>
            </ul>
            <div className="mt-2 text-sm text-gray-300">
              <p>Your current gold: {user?.gold || 0}</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-300">
                Party Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-300">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                className="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="regionId" className="block text-sm font-medium text-gray-300">
                Region
              </label>
              <input
                type="text"
                id="regionId"
                name="regionId"
                value={user?.region?.name || 'No region selected'}
                disabled
                className="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => navigate('/profile')}
                className="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  loading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {loading ? 'Creating...' : 'Create Party'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateParty; 