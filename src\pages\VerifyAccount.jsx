import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import api from "../services/api/api";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import { useRedirectIfAuthenticated } from "../hooks/useRedirectIfAuthenticated";

export default function VerifyAccount() {
  // Redirect to home if user is already authenticated
  useRedirectIfAuthenticated();
  const [status, setStatus] = useState("verifying");
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    const token = searchParams.get("token");

    if (!token) {
      setStatus("invalid");
      return;
    }

    const verify = async () => {
      try {
        await api.post(`/auth/verify-account`,{token});
        setStatus("success");
        showSuccessToast("Account verified! You can now log in.");
        setTimeout(() => navigate("/login"), 3000);
      } catch (error) {
        showErrorToast(error);
        setStatus("failed");
      }
    };

    verify();
  }, [searchParams, navigate]);

  return (
    <div className="h-screen flex items-center justify-center text-white">
      {status === "verifying" && <p>Verifying your account...</p>}
      {status === "success" && <p>Your account has been verified! Redirecting to login...</p>}
      {status === "failed" && <p>Verification failed. Invalid or expired token.</p>}
      {status === "invalid" && <p>Invalid verification link.</p>}
    </div>
  );
}
