import { api } from "./api";
import { User } from "../../types/user";
import { UserWarStatistics } from "../../types/warAnalytics";

// Define interfaces for request and response data
export interface UpdateProfileDto {
  id: number;
  username?: string;
  aboutMe?: string;
  avatarUrl?: string;
}

export interface TrainingData {
  trainingType: "strength" | "intelligence" | "endurance";
  duration: number;
  currency: "gold" | "money";
  amount: number;
}

export interface MessageData {
  content: string;
}

export interface MoneyTransferResponse {
  success: boolean;
  message: string;
  newBalance: number;
}

// Avatar upload response interface
export interface UploadAvatarResponse {
  avatarUrl: string;
  key: string;
  size: number;
  mimeType: string;
}

export const userService = {
  getAllUsers: async (): Promise<User[]> => {
    const response = await api.get("/users");
    return response.data;
  },
  
  getAllUsersCount: async (): Promise<number> => {
    const response = await api.get("/users/count");
    return response.data;
  },
  // Get current user data
  getCurrentUser: async (): Promise<User> => {
    const response = await api.get("/users/me");
    return response.data;
  },

  // Get user by ID
  getUserById: async (id: string | number): Promise<User> => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  // Update user profile
  updateProfile: async (userData: UpdateProfileDto): Promise<User> => {
    const response = await api.patch(`/users/${userData.id}`, userData);
    return response.data;
  },

  // Train user skills
  trainUser: async (trainingData: TrainingData): Promise<User> => {
    const response = await api.post("/users/train", trainingData);
    return response.data;
  },

  // Get user war statistics
  getUserWarStats: async (
    userId?: string | number
  ): Promise<UserWarStatistics> => {
    // If userId is provided, get that user's stats, otherwise get current user's stats
    const url = userId
      ? `/wars/analytics/user/${userId}`
      : "/wars/analytics/user";
    const response = await api.get(url);
    return response.data;
  },

  // Send message to user
  sendMessage: async (
    recipientId: string | number,
    messageData: MessageData
  ): Promise<{ success: boolean; message: string }> => {
    const response = await api.post(
      `/messages/send/${recipientId}`,
      messageData
    );
    return response.data;
  },

  // Send money to user
  sendMoney: async (transferData: {
    fromUserId: number;
    toUserId: number;
    amount: number;
  }): Promise<void> => {
    try {
      const response = await api.post("users/transfer-money", transferData);
      return response.data;
    } catch (error) {
      console.error("Error sending money:", error);
      throw error;
    }
  },

  // Move user to a different region
  moveToRegion: async (regionId: string): Promise<User> => {
    const response = await api.post("/users/move-region", { regionId });
    return response.data;
  },

  // Upload user avatar
  uploadAvatar: async (file: File): Promise<UploadAvatarResponse> => {
    const formData = new FormData();
    formData.append("avatar", file);
    const response = await api.post("/users/avatar", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  // Delete user avatar
  deleteAvatar: async (): Promise<void> => {
    const response = await api.delete("/users/avatar");
    return response.data;
  },
};

export default userService;
