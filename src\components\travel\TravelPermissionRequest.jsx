import React, { useState, useEffect, useRef } from "react";
import { travelService, TravelMode } from "../../services/api/travel.service";
import { showErrorToast } from "../../utils/showErrorToast";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { FaLock, FaCheck, FaCoins, FaBolt, FaInfoCircle } from "react-icons/fa";

const TravelPermissionRequest = ({
  regionId,
  regionName,
  travelMode = TravelMode.REGULAR,
  travelCost,
  travelCurrency,
  onPermissionRequested,
}) => {
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showInfoTooltip, setShowInfoTooltip] = useState(false);
  const tooltipRef = useRef(null);

  // Close tooltip when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
        setShowInfoTooltip(false);
      }
    };

    if (showInfoTooltip) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("touchstart", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
    };
  }, [showInfoTooltip]);

  const handleRequestPermission = async (e) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);
      await travelService.requestTravelPermission({
        destinationRegionId: regionId,
        reason: reason.trim() || undefined,
        travelMode,
      });

      showSuccessToast(
        "Permission request submitted! Travel will start automatically when approved."
      );
      setReason("");
      setShowForm(false);

      if (onPermissionRequested) {
        onPermissionRequested();
      }
    } catch (err) {
      console.error("Error requesting travel permission:", err);
      showErrorToast(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-yellow-900/30 rounded-lg p-4 mb-4">
      <div className="flex items-center space-x-2 mb-3">
        {/* <FaLock className="text-yellow-500 mr-2" /> */}
        <h3 className="text-lg font-semibold text-white">
          Permission Required
        </h3>
        {/* Information Icon with Tooltip */}
        {travelCost && travelCurrency && (
          <div className="relative">
            <button
              type="button"
              onMouseEnter={() => setShowInfoTooltip(true)}
              onMouseLeave={() => setShowInfoTooltip(false)}
              onClick={() => setShowInfoTooltip(!showInfoTooltip)}
              className="text-blue-400 hover:text-blue-300 transition-colors"
            >
              <FaInfoCircle className="w-4 h-4" />
            </button>

            {showInfoTooltip && (
              <div className="absolute right-0 top-6 z-50 w-60 max-w-[90vw] bg-gray-900 border border-gray-700 rounded-lg p-3 shadow-lg">
                <div className="text-blue-300 text-sm">
                  <p className="font-medium mb-2 text-blue-200">
                    Payment & Travel Information:
                  </p>
                  <ul className="space-y-1.5">
                    <li>
                      • The travel cost ({travelCost} {travelCurrency}) will be
                      deducted immediately when you submit this request
                    </li>
                    <li>
                      • If your request is declined, the full amount will be
                      refunded to you
                    </li>
                    <li>
                      •{" "}
                      <strong className="text-blue-200">
                        If approved, travel will start automatically
                      </strong>{" "}
                      - no further action needed
                    </li>
                    <li>
                      • Once submitted, you cannot change your travel mode or
                      cancel the request
                    </li>
                  </ul>
                </div>
                {/* Arrow pointing up */}
                <div className="absolute -top-1 right-3 w-2 h-2 bg-gray-900 border-l border-t border-gray-700 transform rotate-45"></div>
              </div>
            )}
          </div>
        )}
      </div>

      <p className="text-gray-300 mb-4">
        You need permission from the leader of this region's state to travel to{" "}
        {regionName}.
      </p>

      {/* Travel Mode and Cost Information */}
      <div className="bg-gray-800/50 p-3 rounded-lg mb-4">
        <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between text-sm gap-2">
          <div className="flex items-center">
            {travelMode === TravelMode.SPEED ? (
              <>
                <FaBolt className="text-yellow-400 mr-2" />
                <span className="text-yellow-300">Speed Travel</span>
              </>
            ) : (
              <>
                <FaCoins className="text-blue-400 mr-2" />
                <span className="text-blue-300">Regular Travel</span>
              </>
            )}
          </div>

          {travelCost && travelCurrency && (
            <div className="flex items-center">
              {travelCurrency === "gold" ? (
                <FaCoins className="text-yellow-400 mr-1 flex-shrink-0" />
              ) : (
                <span className="flex-shrink-0">💰</span>
              )}
              <span className="text-white font-medium text-sm sm:text-base">
                {travelCost} {travelCurrency}
              </span>
            </div>
          )}
        </div>
      </div>

      {!showForm ? (
        <button
          onClick={() => setShowForm(true)}
          className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-md font-medium"
        >
          Request Permission for{" "}
          {travelMode === TravelMode.SPEED ? "Speed" : "Regular"} Travel
        </button>
      ) : (
        <form onSubmit={handleRequestPermission} className="space-y-3">
          <div>
            <label htmlFor="reason" className="block text-gray-300 mb-1">
              Reason for Visit (Optional)
            </label>
            <textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full bg-gray-800 text-white border border-gray-700 rounded-md p-2"
              rows="3"
              placeholder="Explain why you want to visit this region..."
            ></textarea>
          </div>

          <div className="flex flex-col sm:flex-row sm:space-x-3 space-y-2 sm:space-y-0">
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full sm:w-auto bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md flex items-center justify-center disabled:bg-gray-600 disabled:text-gray-400"
            >
              <FaCheck className="mr-2" />
              {isSubmitting ? "Submitting..." : "Submit Request"}
            </button>

            <button
              type="button"
              onClick={() => setShowForm(false)}
              className="w-full sm:w-auto bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md"
            >
              Cancel
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default TravelPermissionRequest;
