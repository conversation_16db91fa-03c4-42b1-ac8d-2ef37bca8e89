import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import { stateService } from '../services/api/state.service';
import Navbar from '../components/Navbar';
import { generateStateColor } from '../utils/colorGenerator';
import { useAuthGuard } from '../hooks/useAuthGuard';
// import Footer from '../components/common/Footer';

const stateColorMap = new Map();

export default function MapPage() {
  useAuthGuard();
  const [states, setStates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [geoJsonData, setGeoJsonData] = useState(null);

  useEffect(() => {
    const fetchGeoJson = async () => {
      try {
        // Using a cleaner, simplified world boundaries dataset
        const response = await fetch('/world.geojson');
        if (!response.ok) throw new Error('Failed to fetch GeoJSON');
        const data = await response.json();
        setGeoJsonData(data);
      } catch (error) {
        console.error('Error fetching GeoJSON:', error);
        // Fallback to your original source
        try {
          const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');
          // const response = await fetch('https://raw.githubusercontent.com/datasets/geo-countries/master/data/countries.geojson');
          const data = await response.json();
          setGeoJsonData(data);
        } catch (fallbackError) {
          console.error('Fallback GeoJSON also failed:', fallbackError);
        }
      }
    };

    fetchGeoJson();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        const states = await stateService.getAllStates();
        setStates(states);
      } catch (err) {
        setError('Failed to fetch map data');
        console.error('Error fetching map data:', err);
      } finally {
        setLoading(false);
      }
    };
  
    fetchStates();
  }, []);

  const getStateColor = (stateId) => {
    if (!stateColorMap.has(stateId)) {
      stateColorMap.set(stateId, generateStateColor(stateColorMap.size));
    }
    return stateColorMap.get(stateId);
  };

  const findStateAndRegionByCountryName = (countryName) => {
    for (const state of states) {

      const region = state.regions.find(r => r.countryCode === countryName);
      if (region) {
        return { state, region };
      }
    }
    return null;
  };

  const getGeoJSONStyle = (feature) => {
    
    const countryName = feature.id || feature.properties['name'];
    const stateAndRegion = findStateAndRegionByCountryName(countryName);

    return {
      fillColor: stateAndRegion ? getStateColor(stateAndRegion.state.id) : '#2d3748', // Dark gray for unclaimed
      weight: 1,
      opacity: 1,
      color: '#000000', // Black borders like your image
      fillOpacity: stateAndRegion ? 0.8 : 0.3
    };
  };

  const onEachFeature = (feature, layer) => {
    const countryId = feature.id || feature.properties['name'];
    const countryName = feature.properties['name'] || "Unknown";
    const stateAndRegion = findStateAndRegionByCountryName(countryId);

    if (stateAndRegion) {
      const { state, region } = stateAndRegion;
      
      // Simple popup with just essential info
      layer.bindPopup(`
        <div class="p-3 min-w-[200px]">
          <h3 class="font-bold text-lg text-gray-900 mb-2">${region.name}</h3>
          <div class="w-full h-2 mb-2 rounded" style="background-color: ${getStateColor(state.id)}"></div>
          <div class="text-sm text-gray-700 space-y-1">
            <div><strong>State:</strong> ${state.name}</div>
            <div><strong>Population:</strong> ${region.population?.toLocaleString() || 'Unknown'}</div>
          </div>
          <div class="mt-3">
            <a href="/states/${state.id}" style="color:#fff" class="inline-block bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
              View Details
            </a>
          </div>
        </div>
      `);

      // Hover effects
      layer.on({
        mouseover: (e) => {
          const layer = e.target;
          layer.setStyle({
            weight: 2,
            fillOpacity: 0.9,
            color: '#ffffff' // White border on hover
          });
        },
        mouseout: (e) => {
          const layer = e.target;
          layer.setStyle({
            weight: 1,
            fillOpacity: 0.8,
            color: '#000000' // Back to black border
          });
        }
      });
    } else {
      // For unclaimed territories, show basic info
      layer.bindPopup(`
        <div class="p-3">
          <h3 class="font-bold text-lg text-gray-900">${countryName}</h3>
          <p class="text-gray-600 text-sm mt-1">Unclaimed Territory</p>
        </div>
      `);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading map data...</div>
        </div>
      </div>
    );
  }
  
  if (error) return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-900 overflow-hidden">
      <Navbar />
      <div className="h-[calc(100vh-5rem)]">
        <MapContainer
          center={[20, 0]} // World center
          zoom={2}
          style={{ height: '100%', width: '100%' }}
          minZoom={2}
          maxZoom={6} // Limit zoom to keep it simple
          maxBounds={[[-85, -180], [85, 180]]}
        >
          {/* Plain background - no tile layer for cleaner look */}
          <TileLayer
            url="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            attribution=""
          />
          
          {geoJsonData && (
            <GeoJSON
              data={geoJsonData}
              style={getGeoJSONStyle}
              onEachFeature={onEachFeature}
            />
          )}
        </MapContainer>

        {/* Clean Legend */}
        {/* <div className="absolute bottom-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg shadow-xl border border-gray-200 p-4 max-w-xs">
          <h3 className="text-gray-900 font-bold mb-3 text-lg">States Control</h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {states.map(state => (
              <div key={state.id} className="flex items-center gap-3 py-1">
                <div 
                  className="w-4 h-4 rounded border border-gray-300 flex-shrink-0"
                  style={{ backgroundColor: getStateColor(state.id) }}
                />
                <div className="flex-1 min-w-0">
                  <div className="text-gray-900 text-sm font-medium truncate">
                    {state.name}
                  </div>
                  <div className="text-gray-500 text-xs">
                    {state.regions.length} regions
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-3 pt-2 border-t border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-gray-600 rounded border border-gray-300"></div>
              <span className="text-gray-600 text-sm">Unclaimed</span>
            </div>
          </div>
        </div> */}
      </div>
      {/* <Footer /> */}
    </div>
  );
}