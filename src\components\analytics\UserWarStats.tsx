import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { userService } from '../../services/api/user.service';
import { Link } from 'react-router-dom';
import { showErrorToast } from "../../utils/showErrorToast";
import { UserWarStatistics } from '../../types/warAnalytics';

interface UserWarStatsProps {
  userId?: string | number;
}

const UserWarStats: React.FC<UserWarStatsProps> = ({ userId }) => {
  const [stats, setStats] = useState<UserWarStatistics | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [username, setUsername] = useState<string>('');

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        setLoading(true);

        // If userId is provided, fetch that user's stats, otherwise get current user's stats
        let data: UserWarStatistics;
        if (userId) {
          // Get the username for display
          try {
            const userData = await userService.getUserById(userId);
            setUsername(userData.username || '');
          } catch (error) {
            console.error('Failed to fetch username:', error);
          }
          
          // Use the userService to get war stats for the specific user
          try {
            data = await userService.getUserWarStats(userId);
          } catch (error) {
            // Fallback to current user's stats if endpoint for specific user doesn't exist yet
            console.error('Failed to fetch specific user war stats, using current user stats as fallback:', error);
            data = await warService.getUserWarStats();
          }
        } else {
          data = await warService.getUserWarStats();
        }

        setStats(data);
      } catch (error) {
        console.error('Failed to fetch user war statistics:', error);
        showErrorToast('Failed to load war statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchUserStats();
  }, [userId]);

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="grid grid-cols-2 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-700 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div>
        <p className="text-gray-400">No war statistics available</p>
      </div>
    );
  }

  return (
    <div>
      {userId ? (
        <h2 className="text-xl font-bold text-white mb-4">{username ? `${username}'s` : 'User'} War Statistics</h2>
      ) : (
        <h2 className="text-xl font-bold text-white mb-4">Your War Statistics</h2>
      )}

      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Total Wars</h3>
            <span className="text-neonBlue text-2xl">⚔️</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.totalParticipation}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Wars Won</h3>
            <span className="text-green-500 text-2xl">🏆</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.warsWon}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Wars Lost</h3>
            <span className="text-red-500 text-2xl">❌</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.warsLost}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Total Damage</h3>
            <span className="text-yellow-500 text-2xl">💥</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.totalDamageDealt.toLocaleString()}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Highest Damage</h3>
            <span className="text-orange-500 text-2xl">🔥</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.highestDamageInSingleWar.toLocaleString()}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-gray-400">Active Wars</h3>
            <span className="text-purple-500 text-2xl">⚡</span>
          </div>
          <p className="text-2xl font-bold text-white">{stats.currentActiveWars}</p>
        </div>
      </div>

      {stats.mostActiveWarId && (
        <div className="bg-gray-700 p-4 rounded-md mb-6">
          <h3 className="text-lg font-medium text-white mb-2">Most Active War</h3>
          <Link
            to={`/wars/${stats.mostActiveWarId}`}
            className="text-neonBlue hover:text-blue-400"
          >
            View War Details
          </Link>
        </div>
      )}

      <div className="mt-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-medium text-white">Win Rate</h3>
          <span className="text-gray-400 text-sm">
            {stats.warsWon} / {stats.totalParticipation}
          </span>
        </div>
        <div className="w-full bg-gray-600 rounded-full h-4 overflow-hidden">
          {stats.totalParticipation > 0 && (
            <div
              className="bg-green-500 h-full"
              style={{ width: `${(stats.warsWon / stats.totalParticipation) * 100}%` }}
            ></div>
          )}
        </div>
      </div>

      <div className="mt-6 text-center">
        <Link to="/wars" className="inline-block px-4 py-2 bg-neonBlue text-white rounded-md hover:bg-blue-600 transition-colors">
          View All Wars
        </Link>
      </div>
    </div>
  );
};

export default UserWarStats;
