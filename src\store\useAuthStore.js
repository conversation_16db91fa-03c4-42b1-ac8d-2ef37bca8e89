import { create } from "zustand";

const useAuthStore = create((set) => {
  // Debug initial state loading
  const storedUser = localStorage.getItem("user");
  const storedToken = localStorage.getItem("access_token");

  console.log('AuthStore initialization debug:');
  console.log('- Stored user string:', storedUser);
  console.log('- Stored token:', storedToken);

  let parsedUser = null;
  try {
    parsedUser = storedUser ? JSON.parse(storedUser) : null;
    console.log('- Parsed user:', parsedUser);
    console.log('- Parsed username:', parsedUser?.username);
  } catch (error) {
    console.error('- Error parsing stored user:', error);
    localStorage.removeItem("user"); // Clean up corrupted data
  }

  return {
    user: parsedUser,
    access_token: storedToken,

  login: (user, access_token) => {
    console.log('AuthStore login debug:');
    console.log('- User:', user);
    console.log('- Username:', user?.username);
    console.log('- Access token:', access_token);
    console.log('- Token type:', typeof access_token);
    console.log('- Token length:', access_token?.length);

    try {
      localStorage.setItem("access_token", access_token);
      localStorage.setItem("user", JSON.stringify(user));

      // Verify storage immediately
      const storedToken = localStorage.getItem("access_token");
      const storedUser = localStorage.getItem("user");

      console.log('- Storage verification:');
      console.log('  - Token stored:', !!storedToken);
      console.log('  - Token matches:', storedToken === access_token);
      console.log('  - User stored:', !!storedUser);
      console.log('  - User JSON valid:', !!JSON.parse(storedUser));

      set({ user: user, access_token });
      console.log('- AuthStore state updated successfully');
    } catch (error) {
      console.error('AuthStore login error:', error);
      throw error;
    }
  },

  logout: () => {
    // Clean up chat connections before logout
    try {
      // Dynamically import to avoid circular dependency
      import('./useChatStore.js').then(({ default: useChatStore }) => {
        const { reset } = useChatStore.getState();
        reset();
        console.log('AuthStore: Chat connections cleaned up on logout');
      }).catch(error => {
        console.warn('AuthStore: Failed to cleanup chat connections:', error);
      });
    } catch (error) {
      console.warn('AuthStore: Error during chat cleanup:', error);
    }

    localStorage.removeItem("access_token");
    localStorage.removeItem("user");
    set({ user: null, access_token: null });
  },
}});

export default useAuthStore;
