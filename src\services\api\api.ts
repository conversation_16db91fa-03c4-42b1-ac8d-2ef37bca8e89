import axios from 'axios';

const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('API Request:', config.url);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response success:', response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response error:', error.config?.url, error.response?.status);
    
    // Handle network errors
    if (!error.response) {
      return Promise.reject({ message: 'Network error - please check your connection' });
    }

    const isLoginRequest = error.config.url.includes('/auth/login');

    switch (error.response.status) {
      case 401:
        // Only redirect to login if it's not already a login request
        if (!isLoginRequest) {
          console.warn('401 Unauthorized - redirecting to login');
          localStorage.removeItem('access_token');
          window.location.href = '/login';
        }
        break;
      case 403:
        window.location.href = '/home';
        break;
      case 404:
        return Promise.reject({ message: 'Resource not found' });
      case 429:
        return Promise.reject({ message: 'Too many requests - please try again later' });
      case 500:
      case 502:
      case 503:
      case 504:
        return Promise.reject({ message: 'Server error - please try again later' });
    }
    
    // Extract error message from response if available
    const errorMessage = error.response.data?.message || error.message;
    return Promise.reject({ message: errorMessage });
  }
);

export default api;
