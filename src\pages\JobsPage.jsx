import React, { useState, useEffect } from 'react';
import Navbar from '../components/Navbar';
import { showErrorToast } from '../utils/showErrorToast';
import { showSuccessToast } from '../utils/showSuccessToast';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { factoryService } from '../services/api/factory.service';
import useUserDataStore from "../store/useUserDataStore";
import { Link } from 'react-router-dom';
import { AutoMode } from '../types/autoMode';
import WorkResultModal from '../components/factories/WorkResultModal';
import Footer from '../components/common/Footer';

export default function JobsPage() {
  useAuthGuard();
  const [factories, setFactories] = useState([]);
  const [loading, setLoading] = useState(true);
  const { userData, fetchUserData, updateUserData } = useUserDataStore();
  const [selectedFactory, setSelectedFactory] = useState(null);
  const [processingAutoMode, setProcessingAutoMode] = useState(false);
  const [workResult, setWorkResult] = useState(null);
  const [showWorkResultModal, setShowWorkResultModal] = useState(false);

  // Initialize user data when component mounts
  useEffect(() => {
    fetchFactories();
    // fetchUserData();
  }, []);


  const fetchFactories = async () => {
    try {
      // If we don't have user data or region ID, force fetch it
      if (!userData || !userData.region?.id) {
        console.log('No user data or region ID, fetching user data');
        await fetchUserData(true);
      }

      // Get the current user data after potential fetch
      const currentUserData = useUserDataStore.getState().userData;
      if (currentUserData?.region?.id) {
        const factories = await factoryService.getFactoriesByRegion(currentUserData.region.id);
        setFactories(factories);
      } else {
        console.warn('Still no region ID after fetching user data');
        setFactories([]);
        showErrorToast('Unable to determine your region');
      }
    } catch (error) {
      console.error('Error fetching factories:', error);
      showErrorToast('Failed to fetch factories');
    } finally {
      setLoading(false);
    }
  };

  const handleWork = async (factoryId) => {
    try {
      // Get the latest energy value
      const currentEnergy = userData?.energy || 0;

      // Use the factory service to work at the factory with the current energy
      const response = await factoryService.workAtFactory(factoryId, currentEnergy);
      // Update user data
      updateUserData(response.user);

      // Store the work session result and show the modal
      setWorkResult(response.workSession);
      setShowWorkResultModal(true);

      showSuccessToast('Work session completed successfully');
    } catch (error) {
      showErrorToast(error || 'Failed to complete work session');
    }
  };



  const handleAutoWorkSubmit = async (factoryId) => {
    setProcessingAutoMode(true);

    try {
      // Check if user has premium
      if (!userData?.isPremium) {
        showErrorToast('Auto work mode is only available for premium users.');
        return;
      }

      // Check if user already has an active auto mode
      if (userData?.activeAutoMode !== AutoMode.NONE) {
        showErrorToast('You already have an active auto mode. Please disable it first.');
        return;
      }

      // Check if user has energy
      if (userData?.energy <= 0) {
        showErrorToast('You do not have enough energy to work at the factory.');
        return;
      }

      // Enable auto mode
      const response = await factoryService.setAutoWorkMode(factoryId, {
        enable: true,
      });

      showSuccessToast('Auto work mode enabled! The system will work every 30 minutes for 24 hours.');

      // If the response includes the first work session, show it in the modal
      if (response.workSession) {
        setWorkResult(response.workSession);
        setShowWorkResultModal(true);
      }

      // Refresh user data to update energy display and auto mode status
      await fetchUserData(true);
    } catch (error) {
      console.error('Failed to enable auto work mode:', error);
      showErrorToast(error || 'Failed to enable auto work mode');
    } finally {
      setProcessingAutoMode(false);
    }
  };

  const handleDisableAutoWork = async (factoryId) => {
    setProcessingAutoMode(true);

    try {
      // Disable auto mode
      await factoryService.setAutoWorkMode(factoryId, {
        enable: false
      });

      showSuccessToast('Auto work mode disabled successfully.');

      // Refresh user data to update auto mode status
      await fetchUserData(true);
    } catch (error) {
      console.error('Failed to disable auto work mode:', error);
      showErrorToast(error || 'Failed to disable auto work mode');
    } finally {
      setProcessingAutoMode(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading jobs...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Energy Status */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">Your Energy</h2>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
              <span className="text-white text-lg">{userData?.energy || 0}/{userData.isPremium ? 200 : 100}</span>
            </div>
          </div>
          <div className="mt-2 w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((userData?.energy || 0) / (userData?.isPremium ? 200 : 100)) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Auto Work Info */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-white mb-3">Auto Work Mode</h2>
          <div className="bg-gray-700 p-4 rounded-md">
            <p className="text-gray-300 mb-2">
              <span className="text-neonBlue font-medium">Premium users</span> can enable auto work mode for factories. When enabled:
            </p>
            <ul className="list-disc list-inside text-gray-300 space-y-1 ml-2">
              <li>The system will automatically use all available energy to work at the factory every 30 minutes</li>
              <li>Auto work will continue for 24 hours, after which it will automatically stop</li>
              <li>Auto work will stop if the factory reaches its maximum worker capacity</li>
              <li>You can only have one auto mode active at a time (either auto work or auto war participation)</li>
            </ul>

            {userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId && (
              <div className="mt-4 p-3 bg-green-800 bg-opacity-30 rounded-md border border-green-600">
                <p className="text-green-400 font-medium mb-1">Auto Work Mode is Currently Active</p>
                <p className="text-gray-300 text-sm">
                  You have auto work enabled for factory ID: {userData.autoTargetId}
                  {userData?.autoModeExpiresAt && (
                    <span className="block mt-1">
                      Expires: {new Date(userData.autoModeExpiresAt).toLocaleString()}
                    </span>
                  )}
                </p>
              </div>
            )}

            {userData?.activeAutoMode === AutoMode.WAR && (
              <div className="mt-4 p-3 bg-yellow-800 bg-opacity-30 rounded-md border border-yellow-600">
                <p className="text-yellow-400 font-medium">Auto War Mode is Currently Active</p>
                <p className="text-gray-300 text-sm mt-1">
                  You need to disable auto war mode before you can enable auto work mode.
                </p>
              </div>
            )}

            {!userData?.isPremium && (
              <div className="mt-4 p-3 bg-gray-600 rounded-md text-center">
                <p className="text-yellow-400 mb-2">This feature requires a premium account</p>
                <Link to="/shop" className="text-neonBlue hover:text-blue-400">
                  Upgrade to Premium
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Available Factories */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">Available Factories</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {factories.map((factory) => (
              <div
                key={factory.id}
                className="bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors cursor-pointer"
                onClick={() => setSelectedFactory(factory)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold text-white">{factory.name}</h3>
                  <span className={`px-2 py-1 rounded text-sm ${
                    factory.type === 'GOLD' ? 'bg-yellow-500 text-black' : 'bg-green-500 text-white'
                  }`}>
                    {factory.type}
                  </span>
                </div>
                <div className="space-y-2 text-gray-300">
                  <div className="flex justify-between">
                    <span>Wage:</span>
                    <span className="text-white">{factory.wage.toLocaleString()}</span>
                  </div>
                  {/* <div className="flex justify-between">
                    <span>Energy Cost:</span>
                    <span className="text-white">{factory.energyCost}</span>
                  </div> */}
                  <div className="flex justify-between">
                    <span>Workers:</span>
                    <span className="text-white">{factory.workers.length}/{factory.maxWorkers}</span>
                  </div>
                </div>
                <div className="space-y-2 mt-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleWork(factory.id);
                    }}
                    disabled={!userData}
                    className={`w-full px-4 py-2 rounded-md text-white ${
                      !userData
                        ? 'bg-gray-500 cursor-not-allowed'
                        : 'bg-neonBlue hover:bg-blue-600'
                    }`}
                  >
                    Work Here
                  </button>

                  {/* Auto Work Mode Section */}
                  <div className="border-t border-gray-600 pt-3 mt-3">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-300">Auto Work Mode</span>
                      {!userData?.isPremium && (
                        <span className="text-xs text-yellow-400">(Premium Only)</span>
                      )}
                      {userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === factory.id.toString() && (
                        <span className="text-xs text-green-400">Active</span>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAutoWorkSubmit(factory.id);
                        }}
                        disabled={!userData || !userData.isPremium || processingAutoMode ||
                          userData?.activeAutoMode === AutoMode.WORK || userData?.activeAutoMode === AutoMode.WAR}
                        className={`flex-1 px-3 py-1 text-sm rounded-md ${
                          !userData || !userData.isPremium ||
                          userData?.activeAutoMode === AutoMode.WORK || userData?.activeAutoMode === AutoMode.WAR
                            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700 text-white'
                        }`}
                      >
                        {processingAutoMode ? 'Processing...' : 'Enable Auto'}
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDisableAutoWork(factory.id);
                        }}
                        disabled={!userData || !userData.isPremium || processingAutoMode ||
                          !(userData?.activeAutoMode === AutoMode.WORK)}
                        className={`flex-1 px-3 py-1 text-sm rounded-md ${
                          !userData || !userData.isPremium ||
                          !(userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === factory.id.toString())
                            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                            : 'bg-red-600 hover:bg-red-700 text-white'
                        }`}
                      >
                        {processingAutoMode ? 'Processing...' : 'Disable Auto'}
                      </button>
                    </div>

                    {!userData?.isPremium && (
                      <div className="mt-2 text-xs text-center">
                        <Link to="/shop" className="text-neonBlue hover:text-blue-400">
                          Upgrade to Premium
                        </Link>
                      </div>
                    )}

                    {userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === factory.id.toString() && (
                      <div className="mt-2 text-xs text-center text-green-400">
                        Auto work is active for this factory
                        {userData?.autoModeExpiresAt && (
                          <div className="mt-1">
                            Expires: {new Date(userData.autoModeExpiresAt).toLocaleString()}
                          </div>
                        )}
                      </div>
                    )}

                    {userData?.activeAutoMode === AutoMode.WAR && (
                      <div className="mt-2 text-xs text-center text-yellow-400">
                        You have auto mode active for war participation
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Work History */}
        {/* <div className="bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Work History</h2>
          <div className="space-y-4">
            {userData?.workSessions?.map((work) => (
              <div key={work.id} className="bg-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-white font-medium">{work.factory.name}</h3>
                    <p className="text-gray-400 text-sm">
                      {new Date(work.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-green-400">+{work.wageEarned.toLocaleString()} Money</p>
                    <p className="text-yellow-400">+{work.resourceEarned.toLocaleString()} {work.factory.type}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div> */}
      </div>

      {/* Factory Details Modal */}
      {selectedFactory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-xl font-semibold text-white">{selectedFactory.name}</h2>
              <button
                onClick={() => setSelectedFactory(null)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-400">Type:</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  selectedFactory.type === 'GOLD' ? 'bg-yellow-500 text-black' : 'bg-green-500 text-white'
                }`}>
                  {selectedFactory.type}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Wage:</span>
                <span className="text-white">{selectedFactory.wage.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Energy Cost:</span>
                <span className="text-white">{selectedFactory.energyCost}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Workers:</span>
                <span className="text-white">{selectedFactory.workers.length}/{selectedFactory.maxWorkers}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Resource per Work:</span>
                <span className="text-white">{selectedFactory.resourcePerWork}</span>
              </div>

              {/* Auto Work Mode Info */}
              <div className="border-t border-gray-700 pt-4 mt-4">
                <h3 className="text-white font-medium mb-2">Auto Work Mode</h3>
                <p className="text-gray-300 text-sm mb-3">
                  Premium users can enable auto work mode to automatically work at this factory every 10 minutes for 24 hours.
                </p>

                {userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === selectedFactory.id.toString() && (
                  <div className="bg-green-800 bg-opacity-30 p-3 rounded-md mb-3 border border-green-600">
                    <p className="text-green-400 text-sm font-medium">
                      Auto work is currently active for this factory
                      {userData?.autoModeExpiresAt && (
                        <div className="mt-1 text-xs">
                          Expires: {new Date(userData.autoModeExpiresAt).toLocaleString()}
                        </div>
                      )}
                    </p>
                  </div>
                )}

                {userData?.activeAutoMode === AutoMode.WAR && (
                  <div className="bg-yellow-800 bg-opacity-30 p-3 rounded-md mb-3 border border-yellow-600">
                    <p className="text-yellow-400 text-sm font-medium">
                      You have auto mode active for war participation. You can only have one auto mode active at a time.
                    </p>
                  </div>
                )}

                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setSelectedFactory(null);
                      handleAutoWorkSubmit(selectedFactory.id);
                    }}
                    disabled={!userData || !userData.isPremium || processingAutoMode ||
                      userData?.activeAutoMode === AutoMode.WORK || userData?.activeAutoMode === AutoMode.WAR}
                    className={`flex-1 px-3 py-2 rounded-md ${
                      !userData || !userData.isPremium ||
                      userData?.activeAutoMode === AutoMode.WORK || userData?.activeAutoMode === AutoMode.WAR
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
                  >
                    {processingAutoMode ? 'Processing...' : 'Enable Auto Work'}
                  </button>

                  <button
                    onClick={() => {
                      setSelectedFactory(null);
                      handleDisableAutoWork(selectedFactory.id);
                    }}
                    disabled={!userData || !userData.isPremium || processingAutoMode ||
                      !(userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === selectedFactory.id.toString())}
                    className={`flex-1 px-3 py-2 rounded-md ${
                      !userData || !userData.isPremium ||
                      !(userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === selectedFactory.id.toString())
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-red-600 hover:bg-red-700 text-white'
                    }`}
                  >
                    {processingAutoMode ? 'Processing...' : 'Disable Auto Work'}
                  </button>
                </div>

                {!userData?.isPremium && (
                  <div className="mt-3 text-center">
                    <Link to="/shop" className="text-neonBlue hover:text-blue-400 text-sm">
                      Upgrade to Premium to use Auto Work
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Work Result Modal */}
      <WorkResultModal
        isOpen={showWorkResultModal}
        onClose={() => setShowWorkResultModal(false)}
        workSession={workResult}
      />
       <Footer />
    </div>
  );
}
