import { api } from "./api";
import {
  State,
  CreateStateDto,
  UpdateStateDto,
  AddRegionDto,
  StateResourcesResponse,
  GovernmentType,
} from "../../types/state";

export const stateService = {
  // Get all states
  getAllStates: async (): Promise<State[]> => {
    const response = await api.get("/states");
    return response.data;
  },

  getAllStatesCount: async (): Promise<number> => {
    const response = await api.get("/states/count");
    return response.data;
  },

  // Get a single state by ID
  getState: async (id: string): Promise<State> => {
    const response = await api.get(`/states/${id}`);
    return response.data;
  },

  getUserState: async (): Promise<State> => {
    const response = await api.get(`/states/my-state`);
    return response.data;
  },


  // Get state resources
  getStateResources: async (id: string): Promise<StateResourcesResponse> => {
    const response = await api.get(`/states/${id}/resources`);
    return response.data;
  },

  // Create a new state
  createState: async (stateData: CreateStateDto): Promise<State> => {
    const response = await api.post("/states", stateData);
    return response.data;
  },

  // Update a state
  updateState: async (
    id: string,
    stateData: UpdateStateDto
  ): Promise<State> => {
    const response = await api.put(`/states/${id}`, stateData);
    return response.data;
  },

  // Add a region to a state
  addRegion: async (
    stateId: string,
    addRegionDto: AddRegionDto
  ): Promise<State> => {
    const response = await api.post(`/states/${stateId}/regions`, addRegionDto);
    return response.data;
  },

  // Remove a region from a state
  removeRegion: async (stateId: string, regionId: string): Promise<State> => {
    const response = await api.delete(`/states/${stateId}/regions/${regionId}`);
    return response.data;
  },

  // Change state leader
  changeLeader: async (stateId: string, userId: number): Promise<State> => {
    const response = await api.post(
      `/states/${stateId}/change-leader/${userId}`
    );
    return response.data;
  },

  // Add an ally to a state
  addAlly: async (stateId: string, allyId: string): Promise<State> => {
    const response = await api.post(`/states/${stateId}/allies/${allyId}`);
    return response.data;
  },

  // Add an enemy to a state
  addEnemy: async (stateId: string, enemyId: string): Promise<State> => {
    const response = await api.post(`/states/${stateId}/enemies/${enemyId}`);
    return response.data;
  },

  // Get state led by the current user (a user can only lead one state)
  getStateLedByUser: async (): Promise<State | null> => {
    const response = await api.get("/states/leader");
    return response.data;
  },

  // Change government type (state leaders only)
  changeGovernmentType: async (stateId: string, governmentType: GovernmentType): Promise<State> => {
    const response = await api.post(`/states/${stateId}/change-government`, { governmentType });
    return response.data;
  },

  // Update border settings (state leaders only)
  updateBorderSettings: async (stateId: string, hasOpenBorders: boolean): Promise<State> => {
    const response = await api.put(`/states/${stateId}`, { hasOpenBorders });
    return response.data;
  },
};
