import { Region } from './region';
import { User } from './user';

export enum GovernmentType {
  REPUBLIC = 'republic',
  DICTATORSHIP = 'dictatorship'
}

export interface State {
  id: string;
  name: string;
  description?: string;
  flagUrl?: string;
  leader: User;
  regions: Region[];
  treasury: number;
  resourceReserves: {
    gold?: number;
    oil?: number;
    ore?: number;
    uranium?: number;
    diamonds?: number;
  };
  allies?: string[];
  enemies?: string[];
  isActive: boolean;
  hasOpenBorders: boolean;
  governmentType: GovernmentType;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateStateDto {
  name: string;
  description?: string;
  flagUrl?: string;
  includeLeaderRegion?: boolean;
}

export interface UpdateStateDto {
  name?: string;
  description?: string;
  flagUrl?: string;
  isActive?: boolean;
  hasOpenBorders?: boolean;
  governmentType?: GovernmentType;
}

export interface AddRegionDto {
  regionId: string;
}

export interface StateResourcesResponse {
  treasury: number;
  resourceReserves: {
    gold?: number;
    oil?: number;
    ore?: number;
    uranium?: number;
    diamonds?: number;
  };
  totalResources: {
    gold: { current: number; max: number };
    oil: { current: number; max: number };
    ore: { current: number; max: number };
    uranium: { current: number; max: number };
    diamonds: { current: number; max: number };
  };
} 