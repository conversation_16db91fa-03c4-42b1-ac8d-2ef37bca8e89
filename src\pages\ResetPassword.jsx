import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import api from "../services/api/api";
import { toast } from "react-toastify";
import { useRedirectIfAuthenticated } from "../hooks/useRedirectIfAuthenticated";

export default function ResetPassword() {
  // Redirect to home if user is already authenticated
  useRedirectIfAuthenticated();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token"); // Extract token from URL
  const navigate = useNavigate();

  const handleResetPassword = async (e) => {
    e.preventDefault();

    if (!token) {
      toast.error("Invalid or missing reset token.");
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match.");
      return;
    }

    try {
      setLoading(true);
      await api.post("/auth/reset-password", { token, password });

      toast.success("Password reset successfully! Please log in.");
      navigate("/login");
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to reset password.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0F0F10] via-[#1C1C1E] to-[#1E2A4A] flex items-center justify-center px-4">
      <div className="w-full max-w-md bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 shadow-xl font-military">
        <div className="text-center mb-6">
          <img src="/wn-logo.png" alt="WarFront Nations" className="h-14 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white">Reset Password</h2>
        </div>
      <form className="bg-darkCard p-6 rounded-lg" onSubmit={handleResetPassword}>

        <input
          type="password"
          className="w-full mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
          placeholder="New Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />

        <input
          type="password"
          className="w-full mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
          placeholder="Confirm New Password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          required
        />

        <button
          type="submit"
          className="mt-6 w-full bg-[#1C1C1E] border border-white/20 text-white font-bold py-3 rounded-xl transition-all hover:bg-[#2D2D2E]"
          disabled={loading}
        >
          {loading ? "Resetting..." : "Reset Password"}
        </button>
      </form>
    </div>
  </div>
  );
}
