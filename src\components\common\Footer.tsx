import React from "react";
import { <PERSON> } from "react-router-dom";
import { MessageCircle, Users, BookOpen, ExternalLink } from "lucide-react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 border-t border-gray-700 mt-auto">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <img
                src="/wn-logo.png"
                alt="Warfront Nations"
                className="h-8 w-auto mr-3"
              />
              <span className="text-xl font-bold text-neonBlue">
                Warfront Nations
              </span>
            </div>
            <p className="text-gray-400 text-sm mb-4 max-w-md">
              A strategic multiplayer game where you build empires, engage in
              warfare, participate in politics, and dominate the economic
              landscape.
            </p>
            <p className="text-gray-500 text-xs">
              © 2025 Warfront Nations. All rights reserved.
            </p>

            <a
              href="https://www.producthunt.com/products/warfront-nations?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-warfront&#0045;nations"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=981447&theme=light&t=1750502810941"
                alt="Warfront Nations - Rule. Conquer. Dominate the World."
                style={{ width: "250px", height: "54px", marginTop: 20 }}
                width="250"
                height="54"
              />
            </a>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  to="/home"
                  className="text-gray-400 hover:text-neonBlue text-sm transition-colors"
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/wars"
                  className="text-gray-400 hover:text-neonBlue text-sm transition-colors"
                >
                  Wars
                </Link>
              </li>
              <li>
                <Link
                  to="/states"
                  className="text-gray-400 hover:text-neonBlue text-sm transition-colors"
                >
                  States
                </Link>
              </li>
              <li>
                <Link
                  to="/map"
                  className="text-gray-400 hover:text-neonBlue text-sm transition-colors"
                >
                  World Map
                </Link>
              </li>
              <li>
                <Link
                  to="/jobs"
                  className="text-gray-400 hover:text-neonBlue text-sm transition-colors"
                >
                  Jobs
                </Link>
              </li>
            </ul>
          </div>

          {/* Community Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Community</h3>
            <div className="space-y-3">
              {/* Telegram Link */}
              <a
                href="https://t.me/warfront_nations"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-400 hover:text-neonBlue text-sm transition-colors group"
              >
                <MessageCircle className="h-4 w-4 mr-2 group-hover:text-neonBlue" />
                <span>Telegram</span>
                <ExternalLink className="h-3 w-3 ml-1 opacity-50" />
              </a>

              {/* Reddit Link */}
              <a
                href="https://www.reddit.com/r/warfront_nations/"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-400 hover:text-neonBlue text-sm transition-colors group"
              >
                <Users className="h-4 w-4 mr-2 group-hover:text-neonBlue" />
                <span>Reddit</span>
                <ExternalLink className="h-3 w-3 ml-1 opacity-50" />
              </a>

              {/* Wiki Link */}
              <a
                href="https://wiki.warfront-nations.com"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-400 hover:text-neonBlue text-sm transition-colors group"
              >
                <BookOpen className="h-4 w-4 mr-2 group-hover:text-neonBlue" />
                <span>Wiki</span>
                <ExternalLink className="h-3 w-3 ml-1 opacity-50" />
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-500 text-xs mb-4 md:mb-0">
              Made with ❤️ for strategy game enthusiasts
            </div>
            <div className="flex space-x-6">
              <a
                href="https://t.me/warfront_nations"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-neonBlue transition-colors"
                title="Join our Telegram"
              >
                <MessageCircle className="h-5 w-5" />
              </a>
              <a
                href="https://www.reddit.com/r/warfront_nations"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-neonBlue transition-colors"
                title="Visit our Reddit"
              >
                <Users className="h-5 w-5" />
              </a>
              <a
                href="https://wiki.warfront-nations.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-neonBlue transition-colors"
                title="Read the Wiki"
              >
                <BookOpen className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
